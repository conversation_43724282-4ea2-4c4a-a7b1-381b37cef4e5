package com.example.pdf.lyricist.i18n

import cafe.adriel.lyricist.LyricistStrings
import com.example.pdf.lyricist.Locales
import com.example.pdf.lyricist.Strings

@LyricistStrings(languageTag = Locales.FR)
val StringsFr = Strings(
  ok = "OK",
  go = "Aller",
  all = "Tout",
  recent = "Récent",
  bookmarks = "Signets",
  tools = "Outils",
  // Empty states
  noFilesFound = "aucun fichier trouvé",
  noFilesFoundWithType = "Aucun fichier %s trouvé",
  noBookmarks = "aucun signet",
  noBookmarksMessage = "Vous n'avez pas encore ajouté de signets.",
  // Search
  searchPlaceholder = "Rechercher...",
  // Settings
  feedback = "Commentaires",
  privacyPolicy = "Politique de confidentialité",
  version = "Version %s",
  settings = "Paramètres",
  // Document actions
  rename = "Renommer",
  renamedSuccessfully = "Renommé avec succès",
  cancel = "Annuler",
  delete = "Supprimer",
  deleteConfirmSingle = "Êtes-vous sûr de vouloir supprimer ce fichier ?",
  deleteConfirmMultiple = "Êtes-vous sûr de vouloir supprimer %d fichiers ?",
  confirmDeletion = "Confirmer la suppression",
  detail = "Détail",
  share = "Partager",
  // PDF password
  setPassword = "Définir un mot de passe",
  setPasswordDescription = "Créez un mot de passe pour protéger votre PDF.",
  removePassword = "Supprimer le mot de passe",
  removePasswordDescription = "La protection par mot de passe sera supprimée du PDF.",
  password = "Mot de passe",
  enterPassword = "Entrer le mot de passe",
  enterPasswordDescription = "Ce PDF est protégé par mot de passe. Veuillez entrer le mot de passe pour l'ouvrir.",
  incorrectPassword = "Mot de passe incorrect",
  passwordCannotBeEmpty = "Le mot de passe ne peut pas être vide",
  passwordSetSuccessfully = "Mot de passe défini avec succès",
  failedToSetPassword = "Échec de la définition du mot de passe",
  passwordRemovedSuccessfully = "Mot de passe supprimé avec succès",
  incorrectPasswordOrFailed = "Mot de passe incorrect ou échec de la suppression",
  unlock = "Déverrouiller",
  // Success screen
  convertedSuccessfully = "Converti avec succès",
  scannedSuccessfully = "Scanné avec succès",
  lockedSuccessfully = "Verrouillé avec succès",
  unlockedSuccessfully = "Déverrouillé avec succès",
  open = "Ouvrir",
  // Tools
  import = "Importer",
  image = "Image",
  item = "Élément",
  convert = "Convertir",
  importFile = "Importer un fichier",
  imageToPdf = "Image vers PDF",
  wordToPdf = "Word vers PDF",
  scanToPdf = "Scanner vers PDF",
  convertToPdf = "Convertir en PDF",
  lockPdf = "Verrouiller PDF",
  unlockPdf = "Déverrouiller PDF",
  annotate = "Annoter",
  signature = "Signature",
  // Tips and dialogs
  reorderTip = "Appuyez longuement et faites glisser pour réorganiser",
  dismissTips = "ignorer les conseils",
  leaveNow = "Partir maintenant ?",
  leaveEditorWarning = "Vos modifications et images ne seront pas sauvegardées. Êtes-vous sûr de vouloir partir ?",
  leave = "Partir",
  deletePageTitle = "Supprimer cette page ?",
  deletePageWarning = "La page supprimée ne peut pas être récupérée. Êtes-vous sûr de vouloir continuer ?",
  leaveEditModeTitle = "Partir maintenant ?",
  leaveEditModeWarning = "Vos modifications n'ont pas encore été sauvegardées. Voulez-vous les sauvegarder maintenant ?",
  // Document details
  details = "Détails",
  fileName = "Nom du fichier",
  storagePath = "Chemin de stockage",
  lastModified = "Dernière modification",
  lastViewed = "Dernière consultation",
  noData = "--",
  fileSize = "Taille du fichier",
  // Sort
  sortBy = "Trier par",
  selected = "Sélectionné",
  selectAll = "Tout sélectionner",
  // Loading
  loading = " Chargement...",
  // Bottom sheet
  confirm = "Confirmer",
  // Word to PDF errors
  failedToLoad = "Échec du chargement",
  savedToDocuments = "Sauvegardé dans le dossier Documents : %s",
  savedToInternal = "Sauvegardé dans le stockage interne : %s",
  failedToSavePdf = "Échec de la sauvegarde PDF : %s",
  convertFailed = "Échec de la conversion",
  // Rating and feedback
  feedbackGuideMessage = "Nous sommes là pour vous aider et améliorer les choses pour vous.",
  skipForNow = "Ignorer pour l'instant",
  ratingGuideTitle = "Aimez-vous utiliser notre application ?",
  ratingGuideSubtitle = "Faites-le nous savoir avec une évaluation rapide !",
  // Permission
  permissionRequired = "Autorisation requise",
  notice = "Avis",
  // Notification permission
  allowNotification = "Autoriser les notifications",
  notificationPermissionMessage = "Pour rester informé, veuillez autoriser les notifications.",
  grant = "Accorder",
  // Ad messages
  adLoadingFailed = "Échec du chargement de la publicité.",
  adRewardWarning = "Si vous partez avant la fin de la publicité, vous ne serez pas récompensé.",
  loadingAd = "Chargement de la publicité...",
  adLabel = " PUB ",
  // Notification
  tryNow = "Essayer maintenant",
  // Image cropper
  rotateLeft = "Rotation à gauche",
  rotateRight = "Rotation à droite",
  flipHorizontal = "Retourner horizontalement",
  flipVertical = "Retourner verticalement",
  // PDF picker
  selectAFile = "Sélectionner un fichier",
  search = "Rechercher",
  encrypted = "Chiffré",
  unencrypted = "Non chiffré",
  // Toast messages
  failedToDelete = "Échec de la suppression du/des fichier(s)",
  failedToRename = "Échec du renommage du fichier",
  convertSuccessful = "conversion réussie : %s",
  selectedImages = "%d images sélectionnées.",
  imageSelectionCancelled = "Sélection d'image annulée.",
  selectedImagesWithCustomPicker = "%d images sélectionnées avec le sélecteur personnalisé.",
  failedToLoadDocument = "Échec du chargement de %s"
)
