package com.example.pdf.lyricist.i18n

import cafe.adriel.lyricist.LyricistStrings
import com.example.pdf.lyricist.Locales
import com.example.pdf.lyricist.Strings

@LyricistStrings(languageTag = Locales.JA)
val StringsJa = Strings(
  ok = "OK",
  go = "移動",
  all = "すべて",
  recent = "最近",
  bookmarks = "ブックマーク",
  tools = "ツール",
  // Empty states
  noFilesFound = "ファイルが見つかりません",
  noFilesFoundWithType = "%sファイルが見つかりません",
  noBookmarks = "ブックマークなし",
  noBookmarksMessage = "まだブックマークを追加していません。",
  // Search
  searchPlaceholder = "検索...",
  // Settings
  feedback = "フィードバック",
  privacyPolicy = "プライバシーポリシー",
  version = "バージョン %s",
  settings = "設定",
  // Document actions
  rename = "名前を変更",
  renamedSuccessfully = "名前の変更が完了しました",
  cancel = "キャンセル",
  delete = "削除",
  deleteConfirmSingle = "このファイルを削除してもよろしいですか？",
  deleteConfirmMultiple = "%d個のファイルを削除してもよろしいですか？",
  confirmDeletion = "削除の確認",
  detail = "詳細",
  share = "共有",
  // PDF password
  setPassword = "パスワードを設定",
  setPasswordDescription = "PDFを保護するためのパスワードを作成してください。",
  removePassword = "パスワードを削除",
  removePasswordDescription = "PDFからパスワード保護が削除されます。",
  password = "パスワード",
  enterPassword = "パスワードを入力",
  enterPasswordDescription = "このPDFはパスワードで保護されています。開くにはパスワードを入力してください。",
  incorrectPassword = "パスワードが間違っています",
  passwordCannotBeEmpty = "パスワードは空にできません",
  passwordSetSuccessfully = "パスワードが正常に設定されました",
  failedToSetPassword = "パスワードの設定に失敗しました",
  passwordRemovedSuccessfully = "パスワードが正常に削除されました",
  incorrectPasswordOrFailed = "パスワードが間違っているか削除に失敗しました",
  unlock = "ロック解除",
  // Success screen
  convertedSuccessfully = "変換が完了しました",
  scannedSuccessfully = "スキャンが完了しました",
  lockedSuccessfully = "ロックが完了しました",
  unlockedSuccessfully = "ロック解除が完了しました",
  open = "開く",
  // Tools
  import = "インポート",
  image = "画像",
  item = "アイテム",
  convert = "変換",
  importFile = "ファイルをインポート",
  imageToPdf = "画像をPDFに",
  wordToPdf = "WordをPDFに",
  scanToPdf = "スキャンをPDFに",
  convertToPdf = "PDFに変換",
  lockPdf = "PDFをロック",
  unlockPdf = "PDFのロック解除",
  annotate = "注釈",
  signature = "署名",
  // Tips and dialogs
  reorderTip = "長押ししてドラッグして並び替え",
  dismissTips = "ヒントを閉じる",
  leaveNow = "今すぐ終了しますか？",
  leaveEditorWarning = "変更と画像は保存されません。本当に終了しますか？",
  leave = "終了",
  deletePageTitle = "このページを削除しますか？",
  deletePageWarning = "削除されたページは復元できません。続行してもよろしいですか？",
  leaveEditModeTitle = "今すぐ終了しますか？",
  leaveEditModeWarning = "変更がまだ保存されていません。今すぐ保存しますか？",
  // Document details
  details = "詳細",
  fileName = "ファイル名",
  storagePath = "保存パス",
  lastModified = "最終更新",
  lastViewed = "最終閲覧",
  noData = "--",
  fileSize = "ファイルサイズ",
  // Sort
  sortBy = "並び替え",
  selected = "選択済み",
  selectAll = "すべて選択",
  // Loading
  loading = " 読み込み中...",
  // Bottom sheet
  confirm = "確認",
  // Word to PDF errors
  failedToLoad = "読み込みに失敗しました",
  savedToDocuments = "ドキュメントフォルダに保存されました：%s",
  savedToInternal = "内部ストレージに保存されました：%s",
  failedToSavePdf = "PDFの保存に失敗しました：%s",
  convertFailed = "変換に失敗しました",
  // Rating and feedback
  feedbackGuideMessage = "私たちはあなたを支援し、より良いサービスを提供するためにここにいます。",
  skipForNow = "今はスキップ",
  ratingGuideTitle = "私たちのアプリを気に入っていただけましたか？",
  ratingGuideSubtitle = "簡単な評価でお聞かせください！",
  // Permission
  permissionRequired = "権限が必要です",
  notice = "お知らせ",
  // Notification permission
  allowNotification = "通知を許可",
  notificationPermissionMessage = "情報を受け取るために、通知の権限を許可してください。",
  grant = "許可",
  // Ad messages
  adLoadingFailed = "広告の読み込みに失敗しました。",
  adRewardWarning = "広告が終了する前に離れると、報酬を受け取れません。",
  loadingAd = "広告を読み込み中...",
  adLabel = " 広告 ",
  // Notification
  tryNow = "今すぐ試す",
  // Image cropper
  rotateLeft = "左に回転",
  rotateRight = "右に回転",
  flipHorizontal = "水平に反転",
  flipVertical = "垂直に反転",
  // PDF picker
  selectAFile = "ファイルを選択",
  search = "検索",
  encrypted = "暗号化済み",
  unencrypted = "暗号化なし",
  // Toast messages
  failedToDelete = "ファイルの削除に失敗しました",
  failedToRename = "ファイル名の変更に失敗しました",
  convertSuccessful = "変換成功：%s",
  selectedImages = "%d枚の画像を選択しました。",
  imageSelectionCancelled = "画像の選択がキャンセルされました。",
  selectedImagesWithCustomPicker = "カスタムピッカーで%d枚の画像を選択しました。",
  failedToLoadDocument = "%sの読み込みに失敗しました",
  // Signature pad
  color = "色",
  size = "サイズ"
)
